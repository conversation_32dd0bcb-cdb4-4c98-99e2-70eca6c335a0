using System.Collections.Generic;
using System.Threading;
using _FeatureHub.Attributes.Core;
using AssetKits.ParticleImage;
using Cysharp.Threading.Tasks;
using OnePuz.Data;
using OnePuz.Extensions;
using OnePuz.Utilities;
using PrimeTween;
using Sirenix.OdinInspector;
using Spine.Unity;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

namespace OnePuz.UI
{
    [ReferenceInBackground]
    public class UIPanelCongratulation : UIBasePanel
    {
        private readonly int _hashFadeIn = Animator.StringToHash("UI_FadeIn");
        private readonly int _hashFadeOut = Animator.StringToHash("UI_FadeOut");

        [FoldoutGroup("Animator")]
        [SerializeField, ReferenceValue]
        private Animator _animator;

        [FoldoutGroup("Image Effect")]
        [SerializeField, ReferenceValue("ShockwaveFX")]
        private ParticleImage _shockwaveFX;

        [FoldoutGroup("UI Component")]
        [SerializeField, ReferenceValue("ContainerRewards")]
        private CurvedGridLayout _rewardsContainer;

        [FoldoutGroup("Layout Settings")]
        [SerializeField]
        private int _maxColumns = 4;

        [SerializeField, FoldoutGroup("Positions")]
        private RectTransform _positionTreasure;
        [SerializeField, FoldoutGroup("Positions")]
        private RectTransform _positionCloseButton;
        [SerializeField, FoldoutGroup("Positions")]
        private RectTransform _positionCloseButtonWithTreasure;

        [SerializeField, ReferenceValue("btnClose")]
        private Button _btnClose;

        [SerializeField, ReferenceValue("@_btnClose/Contents")]
        private Graphic _graphicClose;

        #region Runtime

        private List<RewardData> _rewardData;
        private readonly List<UIRewardItem> _rewardItems = new List<UIRewardItem>();
        private IContainRewardTarget[] _targetPanels;
        private UITreasure _treasureController;

        #endregion

        protected override async UniTask TransitionInAsync(CancellationToken cancellationToken)
        {
            await _animator.PlayManual(_hashFadeIn, 0.6f);
        }

        protected override async UniTask TransitionOutAsync(CancellationToken cancellationToken)
        {
            await UniTask.CompletedTask;
        }

        protected override void OnBeforeFocus()
        {
            _btnClose.onClick.AddListener(CloseOnClicked);
            _btnClose.SetActive(false);

            pArgs.TryGetData("target_panels", out _targetPanels);
            pArgs.TryGetData("reward_data", out _rewardData);
            pArgs.TryGetData("treasure", out _treasureController);

            // Setup chest if needed
            if (_treasureController)
            {
                _rewardData = _treasureController.Rewards;
            }

            SetupRewardsLayout();

            if (_treasureController)
            {
                PlayChestOpeningAnimation().Forget();
            }
        }

        protected override void OnAfterFocus()
        {
            if (!_treasureController)
            {
                ShowRewards();
                
                _btnClose.GetComponent<RectTransform>().anchoredPosition = _positionCloseButton.anchoredPosition;
            }
            else
            {
                _btnClose.GetComponent<RectTransform>().anchoredPosition = _positionCloseButtonWithTreasure.anchoredPosition;
            }
        }

        protected override void OnBeforeLostFocus()
        {
            _shockwaveFX.Stop();
            _shockwaveFX.Clear();

            _btnClose.onClick.RemoveListener(CloseOnClicked);

            _rewardItems.Clear();
            _rewardData = null;
        }

        protected override void OnAfterLostFocus()
        {
            base.OnAfterLostFocus();

            if (_treasureController)
            {
                _treasureController.ReturnToOriginalContainer();
                _treasureController = null;
            }
        }

        private async UniTask PlayChestOpeningAnimation()
        {
            await _treasureController.ShowAsync(_container, _positionTreasure.anchoredPosition);

            _shockwaveFX.Play();

            ShowRewards(fromChest: true);
        }

        private void SetupRewardsLayout()
        {
            if (_rewardData == null || _rewardData.Count == 0)
                return;

            // Calculate optimal columns based on reward count
            var optimalColumns = CalculateOptimalColumns(_rewardData.Count, _maxColumns);
            var rows = Mathf.CeilToInt((float)_rewardData.Count / optimalColumns);

            // Use the existing Setup function with optimal rows and columns
            // Set updateContinuously too false to manually control item positions
            _rewardsContainer.Setup(rows, optimalColumns, false, true);
        }

        private int CalculateOptimalColumns(int totalItems, int maxColumns)
        {
            if (totalItems <= maxColumns)
                return totalItems;

            // Find optimal column count for best layout appearance
            // Example: 5 items with max 4 columns -> better use 3 columns (2 rows: 3+2)
            for (int cols = maxColumns; cols >= 1; cols--)
            {
                int rows = Mathf.CeilToInt((float)totalItems / cols);
                // If last row fills at least 2/3 of columns
                if (totalItems % cols == 0 || totalItems % cols >= cols * 0.66f)
                    return cols;
            }

            return Mathf.Min(totalItems, maxColumns);
        }

        private void ShowRewards(bool fromChest = false)
        {
            _rewardItems.Clear();

            for (var i = 0; i < _rewardData.Count; i++)
            {
                var rewardItem = UIShortcut.SpawnRewardItem(_rewardData[i], _rewardsContainer.transform);
                _rewardItems.Add(rewardItem);

                rewardItem.CachedRectTransform.localScale = Vector3.zero;

                // Get position from layout
                Vector3 position = _rewardsContainer.GetPositionForIndex(i, _rewardData.Count);
                position.z = 0;
                if (!fromChest)
                {
                    rewardItem.CachedRectTransform.localPosition = position;
                }
                else
                {
                    rewardItem.CachedRectTransform.position = _positionTreasure.position;
                    Tween.LocalPosition(rewardItem.CachedRectTransform, position, 0.75f, Ease.OutBack, startDelay: 0.1f * i);
                }

                rewardItem.Setup(_rewardData[i]);
                rewardItem.PlayAppearAnimation(0.1f * i);
            }

            // Show tap to continue button after all rewards have appeared
            Tween.Delay(0.5f + 0.1f * _rewardData.Count, ActiveTapToContinue);
        }

        private void ActiveTapToContinue()
        {
            _btnClose.SetActive(true);
            Tween.Alpha(_graphicClose, 0, 1, 0.5f);
        }

        private void CloseOnClicked()
        {
            if (_targetPanels != null && _targetPanels.Length > 0)
            {
                UIShortcut.ClaimRewardsAsync(_rewardItems, _targetPanels).Forget();
                _rewardItems.Clear();
            }
            else
            {
                DataShortcut.ClaimRewards(_rewardData);
            }

            Close();
        }
    }
}