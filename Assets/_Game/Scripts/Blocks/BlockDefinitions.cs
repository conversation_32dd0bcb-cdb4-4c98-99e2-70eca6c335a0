using System.Collections.Generic;
using System.Linq;
using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.Serialization;

namespace OP.BlockSand
{
    [CreateAssetMenu(fileName = "BlockDefinitions.asset", menuName = "Game/BlockDefinitions")]
    public class BlockDefinitions : ScriptableObject
    {
        [Header("Settings")]
        public float blockSize;

        [Header("Definitions")]
        [SerializeField]
        private List<TexturePatternDefinition> _blockPatternDefinitions = new();

        public Sprite GetRandomTexture(BlockType type)
        {
            var randomPatternIndex = Random.Range(0, _blockPatternDefinitions.Count);
            return (from t in _blockPatternDefinitions[randomPatternIndex].textures where t.type == type select t.sprite).FirstOrDefault();
        }

        [System.Serializable]
        public class TexturePatternDefinition
        {
            public List<TextureDefinition> textures;
        }

        [System.Serializable]
        public class TextureDefinition
        {
            public BlockType type;
            public Sprite sprite;
        }

#if UNITY_EDITOR
        [Header("Tools")]
        [SerializeField]
        private string _textureFolderPath = "Assets/_Game/Arts/In-game/Blocks";

        [SerializeField]
        private string _patternFolderNamePrefix = "Pattern_";

        [Button]
        private void SetupTextureDefinitions()
        {
            _blockPatternDefinitions.Clear();
            // Check number of pattern in folder 
            var patternFolders = UnityEditor.AssetDatabase.GetSubFolders(_textureFolderPath);
            foreach (var folder in patternFolders)
            {
                OLogger.Log($"Folder: {folder}");
                if (!folder.Contains(_patternFolderNamePrefix)) continue;

                var pattern = new TexturePatternDefinition
                {
                    textures = new List<TextureDefinition>()
                };

                var files = System.IO.Directory.GetFiles(folder, "*.png");
                foreach (var file in files)
                {
                    OLogger.Log($"File: {file}");
                    var number = int.Parse(file.Substring(file.LastIndexOf('/') + 1, 1)) - 1 + 100;
                    pattern.textures.Add(new TextureDefinition()
                    {
                        type = (BlockType)number,
                        sprite = UnityEditor.AssetDatabase.LoadAssetAtPath<Sprite>(file)
                    });
                }

                _blockPatternDefinitions.Add(pattern);
            }


            UnityEditor.EditorUtility.SetDirty(this);
        }
#endif
    }
}