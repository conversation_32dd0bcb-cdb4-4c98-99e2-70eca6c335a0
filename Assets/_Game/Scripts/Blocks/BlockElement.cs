using UnityEngine;

namespace OP.BlockSand
{
    public class BlockElement : MonoBehaviour
    {
        public const int PixelNumberSize = 8;
        
        [SerializeField]
        private SpriteRenderer _renderer;

        [SerializeField]
        private Vector2Int _coordinate;
        
        public Vector2Int Coordinate => _coordinate;
        
        public BlockType BlockType { get; private set; }

        public void Init(BlockType blockType, Vector2Int coordinate, Sprite blockSprite)
        {
            BlockType = blockType;
            _coordinate = coordinate;
            _renderer.sprite = blockSprite;
        }
        
        public void UpdateSortOrder(int value)
        {
            _renderer.sortingOrder = value;
        }
        
        public Color32[] GetPixels()
        {
            return _renderer.sprite.texture.GetPixels32();
        }
    }
}