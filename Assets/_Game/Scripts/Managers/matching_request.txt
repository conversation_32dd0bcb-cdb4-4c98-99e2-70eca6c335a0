Mechanic cơ bản của game là đặt các khối Shape vào <PERSON>xelWorld, các kh<PERSON>i <PERSON>hape tách thành từng pixel và rơi xuống, hành x<PERSON> nh<PERSON>, phần này tôi đã hoàn thành.

Mỗi màu của khối Shape tương ứng với 1 PixelMaterialId khác nhau (từ BlockSand_0 = 100 trở đi).

PixelWorld có kích thước 86x106, có bounding là 1px, coi nh<PERSON> là bứ<PERSON> tườ<PERSON>, không thể tương tác (có PixelMaterialId khác BlockSand) 

Dựa vào PixelMaterialId, ta sẽ sử dụng thuật toán Flood Fill để xác định cụm cát nào trải dài từ bên tr<PERSON>i sang bên phải (tứ<PERSON> là từ xIndex 1 đến xIndex 84) thì sẽ có thể Clear cụm cát đó. Logic này cần sử dụng Unity Job để handle. 

<PERSON><PERSON> diễn hoạt animation Clear cụm cát, nên thay đổi màu sắc của các pixel trong cụm cát thành màu trắng, rồi lại chuyển trở lại màu gốc, chuyển sang màu trắng rồi clear hoàn toàn Pixel. Trong lúc diễn hoạt animation, các pixel đó nên stop Free Falling, để tránh nhầm lẫn pixel. 

Cần thêm logic check Lose, khi có pixel đã Sleep mà nằm từ Row 90 trở lên thì game sẽ thua. Có thể check toàn bộ PixelWorld khác empty (check từ trên xuống sẽ tối ưu số pixel cần check hơn). Logic này cũng nên sử dụng Unity Job để handle.

Các logic liên quan tới Clear cụm cát và check Lose nên xử lý trong Board.cs.