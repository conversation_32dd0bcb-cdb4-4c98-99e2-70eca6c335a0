using System.Linq;
using Sirenix.OdinInspector;
using UnityEngine;
using Random = UnityEngine.Random;

namespace OP.BlockSand
{
    public class ShapeSpawner : MonoBehaviour
    {
        [SerializeField]
        private ShapeDefinition _shapeDefinition;
        
        [SerializeField]
        private Transform[] _spawnPoints;
        
        private readonly Shape[] _shapes = new Shape[3];

        public void Init()
        {
            SpawnAllShapes();
        }
        
        [Button]
        public void SpawnAllShapes()
        {
            foreach (var shape in _shapes)
            {
                if (!shape) continue;
                
                Core.ScenePool.Recycle(shape.gameObject);
            }
            
            for (var i = 0; i < _spawnPoints.Length; i++)
            {
                _shapes[i] = SpawnShape(i);
            }
        }

        private Shape SpawnShape(int index)
        {
            var shape = Core.ScenePool.Spawn(_shapeDefinition.shapePrefab.gameObject).GetComponent<Shape>();
            shape.OTransform.localPosition = _spawnPoints[index].position;
            // shape.Init(_shapeDefinition.shapes[index], (BlockType)Random.Range(100, 107));
            shape.Init(_shapeDefinition.shapes[index], BlockType.Yellow);
            return shape;
        }

        public Shape GetShape(int index)
        {
            return _shapes[index];
        }
        
        public bool AnyShapesLeft()
        {
            return _shapes.Any(t => t);
        }

        public void RemoveShape(int index)
        {
            if (_shapes[index])
            {
                _shapes[index] = null;
            }
        }

        public void Despawn(Shape shape)
        {
            shape.Reset();
            Core.ScenePool.Recycle(shape.gameObject);
        }
        
        public void Reset()
        {
            foreach (var shape in _shapes)
            {
                if (!shape) continue;
                
                Core.ScenePool.Recycle(shape.gameObject);
            }
        }
    }
}