using Cysharp.Threading.Tasks;
using DrawXXL;
using OnePuz.Audio;
using OnePuz.Extensions;
using OnePuz.Manager;
using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.EventSystems;

namespace OP.BlockSand
{
    public class GameManager : BaseManager
    {
        [SerializeField]
        private Camera _camera;

        [SerializeField]
        private SandWorld _sandWorld;

        [SerializeField]
        private PixelMaterialId _drawingMaterialId = PixelMaterialId.Sand;

        [SerializeField]
        private ShapeSpawner _shapeSpawner;

        [SerializeField]
        private PlayerInput _playerInput;

        [SerializeField]
        private Board _board;

        private Vector3? _lastMousePixelPos;

        private bool _canPlaceBlock;
        private bool _placingBlock;

        public override async UniTask LoadAsync()
        {
            _sandWorld.PixelWorld.FrameRate = 60;
            await UniTask.Yield();
            var loading = true;
            _sandWorld.LoadLevel(0, _ => loading = false);
            while (loading)
                await UniTask.Yield();

            _shapeSpawner.Init();
            _playerInput.Init(_camera);
            _board.Init(_sandWorld);

            this.EventSubscribe<OnShapeSelectedEvent>(HandleSelectShape);
            this.EventSubscribe<OnShapeMovedEvent>(HandleMoveShape);
            this.EventSubscribe<OnShapeReleasedEvent>(HandleReleaseShape);
        }

        public override UniTask UnloadAsync()
        {
            this.EventUnsubscribe<OnShapeSelectedEvent>(HandleSelectShape);
            this.EventUnsubscribe<OnShapeMovedEvent>(HandleMoveShape);
            this.EventUnsubscribe<OnShapeReleasedEvent>(HandleReleaseShape);
            return UniTask.CompletedTask;
        }

        public override async UniTask ResetAsync()
        {
            await UniTask.CompletedTask;

            _shapeSpawner.Reset();
            _shapeSpawner.SpawnAllShapes();
            
            _board.ResetGameState();

            _canPlaceBlock = true;
            _placingBlock = false;
        }

        public override void Activate()
        {
            _canPlaceBlock = true;
        }

        private void Update()
        {
            if (!IsInitialized)
                return;

            // HandleInput();
            _board.UpdateGameStateCheck();
            if (_board.ShouldCheckGameState())
            {
                _board.CheckGameState();
            }
        }

        private void HandleInput()
        {
            // For testing purposes
            if (Input.touchCount > 0)
            {
                var touch = Input.GetTouch(0);
                var pixelPos = _sandWorld.PixelWorld.ScreenToPixelPos(touch.position);
                DrawAt(bigBrush: false, pixelPos);
            }
            else if (
                ((Input.GetMouseButton(0) && !Input.GetKey(KeyCode.LeftControl))
                 || (Input.GetMouseButtonDown(0) && Input.GetKey(KeyCode.LeftControl)))
                && !EventSystem.current.IsPointerOverGameObject()
            )
            {
                var isShiftPressed = Input.GetKey(KeyCode.LeftShift);
                var pixelPos = _sandWorld.PixelWorld.MouseToPixelPos(Input.mousePosition);
                DrawAt(isShiftPressed, pixelPos);
            }
            else
            {
                _lastMousePixelPos = null;
            }
        }

        private void HandleSelectShape(OnShapeSelectedEvent e)
        {
            var shape = _shapeSpawner.GetShape(e.shapeIndex);
            if (!shape) return;
            AudioShortcut.PlayChooseBlock();
            shape.HandleSelected(e.worldPosition, _board.DrawableRect);
        }

        private void HandleMoveShape(OnShapeMovedEvent e)
        {
            var shape = _shapeSpawner.GetShape(e.shapeIndex);
            if (!shape) return;

            shape.HandleMoving(e.worldPosition, _board.DrawableRect);
            _board.CheckShape(shape);
            _canPlaceBlock = _board.ValidateDrawShape(shape, out var targetShapePosition, out _);
            DrawBasics2D.PointTag(targetShapePosition, $"{targetShapePosition}", color: Color.blue);

            if (_placingBlock)
                return;
        }

        private void HandleReleaseShape(OnShapeReleasedEvent e)
        {
            var shape = _shapeSpawner.GetShape(e.shapeIndex);
            if (!shape) return;

            shape.HandleRelease();

            if (_canPlaceBlock && !_placingBlock && !e.hasCanceled)
            {
                PlaceShapeAsync(shape, e.shapeIndex).Forget();
            }
            else
            {
                AudioShortcut.PlayPlacedFailed();
                shape.MoveBack();
            }
        }

        private async UniTask PlaceShapeAsync(Shape shape, int shapeIndex)
        {
            _placingBlock = true;
            _canPlaceBlock = false;

            var valid = _board.ValidateDrawShape(shape, out var targetShapePosition, out var pixelPosition);
            if (valid)
            {
                var shapeMoving = true;
                shape.MoveToTarget(targetShapePosition, () => shapeMoving = false);
                await UniTask.WaitUntil(() => !shapeMoving);

                _board.DrawShape(shape, pixelPosition);
                _shapeSpawner.RemoveShape(shapeIndex);
                _shapeSpawner.Despawn(shape);
                AudioShortcut.PlayPlacedBlock();

                if (!_shapeSpawner.AnyShapesLeft())
                    _shapeSpawner.SpawnAllShapes();
            }
            else
            {
                AudioShortcut.PlayPlacedFailed();
                shape.MoveBack();
            }

            _placingBlock = false;
            _canPlaceBlock = true;
        }

        [Button]
        private void TestDraw(Vector2 pixelPos)
        {
            DrawAt(false, pixelPos);
        }

        private void DrawAt(bool bigBrush, Vector3 pixelPos)
        {
            // if (!_lastMousePixelPos.HasValue)
            // {
            //     _sandWorld.PixelWorld.DrawBrushAt(PixelWorldBrushShape.Circle, bigBrush ? 20 : 5, pixelPos.x, pixelPos.y, _drawingMaterialId);
            // }
            // else
            // {
            //     _sandWorld.PixelWorld.DrawLine(PixelWorldBrushShape.Circle, bigBrush ? 20 : 5, _lastMousePixelPos.Value.x, _lastMousePixelPos.Value.y, pixelPos.x, pixelPos.y, _drawingMaterialId);
            // }

            _sandWorld.PixelWorld.DrawPixelAt(pixelPos.x, pixelPos.y, _drawingMaterialId);

            if (_sandWorld.PixelWorld.TryGetPixelAt(pixelPos, out Pixel pixel))
            {
                OLogger.LogNotice($"==> Check pixel at {pixelPos}: Material: {pixel.materialIndex}, Pos: {pixel.x} - {pixel.y}");
            }

            _lastMousePixelPos = pixelPos;
        }
    }
}