using System.Collections.Generic;
using System.Linq;
using DrawXXL;
using UnityEngine;
using Unity.Jobs;
using Unity.Collections;

namespace OP.BlockSand
{
    public class Board : MonoBehaviour
    {
        [SerializeField]
        private PixelMaterials _materialDefinition;
        
        private SandWorld _sandWorld;
        private ClearAnimationSystem _clearAnimationSystem;

        [SerializeField, Sirenix.OdinInspector.ReadOnly]
        private Rect _actualBoardRect;
        [SerializeField, Sirenix.OdinInspector.ReadOnly]
        private Rect _drawableBoardRect;

        private int _worldWidth;
        private int _worldHeight;
        private int _loseThresholdY;
        private int _minValidX; // After left boundary
        private int _maxValidX; // Before right boundary

        [Header("Debug")]
        [SerializeField, Unity.Collections.ReadOnly] private bool _isGameLost = false;
        [SerializeField, Unity.Collections.ReadOnly] private int _activeClusters = 0;
        [SerializeField, Unity.Collections.ReadOnly] private GameStateCheckStatus _checkStatus = GameStateCheckStatus.Idle;

        // Timing to prevent race conditions
        private float _lastDrawTime = 0f;
        private const float DRAW_TO_CHECK_DELAY = 0.1f; // Wait 100ms after drawing before checking game state

        public Rect DrawableRect => _drawableBoardRect;
        public bool IsGameLost => _isGameLost;
        public bool IsCheckingGameState => _checkStatus != GameStateCheckStatus.Idle;

        // Events
        public System.Action OnGameLost;
        public System.Action<int> OnClustersCleared;

        // Track placed shapes and their materials
        // We track by material INDEX (not ID) because that's what pixels store
        private readonly HashSet<int> _activeMaterialIndices = new ();
        private readonly Dictionary<int, int> _materialIndexPixelCounts = new ();

        // For legacy/debug purposes - track material IDs that have been placed
        private readonly List<PixelMaterialId> _placedMaterialIds = new ();

        // Helper mapping to convert Material ID -> Material Index
        private readonly Dictionary<PixelMaterialId, int> _materialIdToIndexMap = new ();

        // Job system state
        private JobHandle _currentJobHandle;
        private GameStateJobData _currentJobData;
        private List<Vector2Int> _pixelsToAnimate = new List<Vector2Int>();
        private float _animationTimer = 0f;
        private const float ANIMATION_DURATION = 1.0f;

        public void Init(SandWorld sandWorld)
        {
            _sandWorld = sandWorld;

            _worldWidth = _sandWorld.Width;
            _worldHeight = _sandWorld.Height;
            _loseThresholdY = _worldHeight + 5;
            _minValidX = 1; // After left boundary
            _maxValidX = _worldWidth - 2; // Before right boundary

            // Initialize clear animation system
            _clearAnimationSystem = GetComponent<ClearAnimationSystem>();
            if (_clearAnimationSystem == null)
            {
                _clearAnimationSystem = gameObject.AddComponent<ClearAnimationSystem>();
            }
            
            _clearAnimationSystem.Initialize(_sandWorld.PixelWorld);

            // Need to minus 2 because of the margin, the level already include border 1px
            var boardPosition = (Vector2)_sandWorld.transform.position;
            var drawableBoardWidth = (float)(_sandWorld.Width - 2) / _sandWorld.PixelsPerUnit;
            var drawableBoardHeight = (float)(_sandWorld.Height - 2) / _sandWorld.PixelsPerUnit;
            _drawableBoardRect = new Rect(boardPosition.x - drawableBoardWidth / 2, boardPosition.y - drawableBoardHeight / 2, drawableBoardWidth, drawableBoardHeight);
            
            var boardWidth = (float)(_sandWorld.Width) / _sandWorld.PixelsPerUnit;
            var boardHeight = (float)(_sandWorld.Height) / _sandWorld.PixelsPerUnit;
            _actualBoardRect = new Rect(boardPosition.x - boardWidth / 2, boardPosition.y - boardHeight / 2, boardWidth, boardHeight);

            // Build material ID to index mapping
            BuildMaterialIdToIndexMap();
        }

        private void BuildMaterialIdToIndexMap()
        {
            _materialIdToIndexMap.Clear();

            if (_materialDefinition?.Materials != null)
            {
                OLogger.Log($"Building material ID to index mapping from {_materialDefinition.Materials.Length} materials:");
                for (int i = 0; i < _materialDefinition.Materials.Length; i++)
                {
                    var material = _materialDefinition.Materials[i];
                    _materialIdToIndexMap[material.id] = i;
                    OLogger.Log($"  Material {material.id} -> index {i}");
                }
            }
            else
            {
                OLogger.LogError("Material definition is null or has no materials!");
            }
        }

        /// <summary>
        /// Get material index from material ID. Returns -1 if not found.
        /// </summary>
        private int GetMaterialIndex(PixelMaterialId materialId)
        {
            if (_materialIdToIndexMap.TryGetValue(materialId, out int index))
            {
                return index;
            }

            OLogger.LogError($"Material ID {materialId} not found in mapping! Available: [{string.Join(", ", _materialIdToIndexMap.Keys)}]");
            return -1;
        }

        /// <summary>
        /// Get material ID from material index. Returns Empty if not found.
        /// </summary>
        private PixelMaterialId GetMaterialId(int materialIndex)
        {
            foreach (var kvp in _materialIdToIndexMap)
            {
                if (kvp.Value == materialIndex)
                    return kvp.Key;
            }
            return PixelMaterialId.Empty;
        }

        public void CheckShape(Shape shape)
        {
            var shapePointBottomLeft = new Vector2(
                shape.OTransform.position.x - shape.Rect.width / 2f, 
                shape.OTransform.position.y - shape.Rect.height / 2f);
            var shapePointBottomLeftInPixel = GetPixelPositionInBoard(shapePointBottomLeft);
            DrawBasics2D.PointTag(shapePointBottomLeft, $"{shapePointBottomLeft} - pixel {shapePointBottomLeftInPixel}", color: Color.green);
        }

        public bool ValidateDrawShape(Shape shape, out Vector3 targetShapePosition, out Vector2 pixelPosition)
        {
            targetShapePosition = Vector3.zero;
            pixelPosition = Vector2.zero;
            
            var shapePointBottomLeft = new Vector3(
                shape.OTransform.position.x - shape.Rect.width / 2f, 
                shape.OTransform.position.y - shape.Rect.height / 2f);
            var shapePointBottomLeftInPixel = GetPixelPositionInBoard(shapePointBottomLeft);
            
            if (shapePointBottomLeftInPixel.x < 1f || shapePointBottomLeftInPixel.y < 1f)
                return false;
            
            // Check if overlap other pixels
            foreach (var blockPixel in shape.GetPixels())
            {
                var pixelPos = new Vector3(shapePointBottomLeftInPixel.x + blockPixel.coordinate.x, shapePointBottomLeftInPixel.y + blockPixel.coordinate.y, 0);
                if (_sandWorld.PixelWorld.TryGetPixelAt(pixelPos, out var pixel))
                {
                    if (pixel.materialIndex != 0)
                        return false;
                }
                else
                    return false;
            }

            targetShapePosition = shape.OTransform.position - shapePointBottomLeft + GetWorldPositionFromPixel(shapePointBottomLeftInPixel);
            pixelPosition = shapePointBottomLeftInPixel;
            return true;
        }
        
        public void DrawShape(Shape shape, Vector2 pixelPosition)
        {
            // Wait for any running game state check jobs to complete before drawing
            // This prevents race condition with job system accessing pixel buffer
            if (_checkStatus != GameStateCheckStatus.Idle)
            {
                _currentJobHandle.Complete();

                // Reset to idle state after completing job
                if (_currentJobData.IsValid)
                {
                    _currentJobData.Dispose();
                }
                _checkStatus = GameStateCheckStatus.Idle;
            }

            // Track materials used in this shape
            var shapeMaterials = new HashSet<PixelMaterialId>();

            foreach (var blockPixel in shape.GetPixels())
            {
                var pixelPos = new Vector3(pixelPosition.x + blockPixel.coordinate.x, pixelPosition.y + blockPixel.coordinate.y, 0);
                var materialId = (PixelMaterialId)(int)blockPixel.blockType;

                // Get material index from ID
                int materialIndex = GetMaterialIndex(materialId);
                if (materialIndex >= 0)
                {
                    OLogger.Log($"Drawing pixel at {pixelPos} with blockType {blockPixel.blockType} -> materialId {materialId} -> index {materialIndex}");
                    DrawAt(pixelPos, blockPixel.color, materialId);

                    // Track this material by index
                    _activeMaterialIndices.Add(materialIndex);

                    // Update pixel count for this material index
                    if (!_materialIndexPixelCounts.ContainsKey(materialIndex))
                        _materialIndexPixelCounts[materialIndex] = 0;
                    _materialIndexPixelCounts[materialIndex]++;

                    // Track this material ID for legacy compatibility
                    shapeMaterials.Add(materialId);
                }
                // Error already logged in GetMaterialIndex()
            }

            // Add new materials to active set (for legacy compatibility)
            foreach (var materialId in shapeMaterials)
            {
                if (!_placedMaterialIds.Contains(materialId))
                {
                    _placedMaterialIds.Add(materialId);
                    OLogger.Log($"New material placed: {materialId}");
                }
            }

            // Record the time when we drew pixels to prevent immediate game state check
            _lastDrawTime = Time.time;

            // Note: Game state check should be called from GameManager Update loop
        }

        private void DrawAt(Vector2 pixelPos, Color32 color, PixelMaterialId materialId)
        {
            _sandWorld.PixelWorld.DrawPixelAt(pixelPos.x, pixelPos.y, color.r, color.g, color.b, color.a, materialId);
        }

        private Vector3 GetPixelPositionInBoard(Vector3 worldPosition)
        {
            var localPositionInBoard = _sandWorld.transform.InverseTransformPoint(worldPosition);
            var positionFromBottomLeft = new Vector3(localPositionInBoard.x + _actualBoardRect.width / 2f, localPositionInBoard.y + _actualBoardRect.height / 2f, 0);
            return _sandWorld.PixelWorld.WorldToPixelPos(positionFromBottomLeft);
        }
        
        private Vector3 GetWorldPositionFromPixel(Vector3 pixelPosition)
        {
            var flooredPixelPosition = new Vector3(Mathf.Floor(pixelPosition.x), Mathf.Floor(pixelPosition.y), 0);
            var localPositionInBoardFromBottomLeft = _sandWorld.PixelWorld.PixelToWorldPos(flooredPixelPosition);
            var localPositionInBoard = new Vector3(localPositionInBoardFromBottomLeft.x - _actualBoardRect.width / 2f, localPositionInBoardFromBottomLeft.y - _actualBoardRect.height / 2f, 0);
            return _sandWorld.transform.TransformPoint(localPositionInBoard);
        }

        /// <summary>
        /// Check for clearable sand clusters and lose condition
        /// Call this from GameManager Update loop
        /// </summary>
        public void CheckGameState()
        {
            if (_isGameLost || _checkStatus != GameStateCheckStatus.Idle)
                return;

            // Wait for any pending pixels to be drawn before checking game state
            // This prevents race condition with PixelWorld.copyPixelScheduledForDrawing()
            if (HasPendingPixelsToDraw())
            {
                OLogger.LogWarning("CheckGameState: Waiting for pending pixels to be drawn");
                return;
            }

            OLogger.Log("CheckGameState: Starting lose check");
            StartLoseCheck();
        }

        /// <summary>
        /// Update game state checking process - call this from GameManager Update
        /// </summary>
        public void UpdateGameStateCheck()
        {
            switch (_checkStatus)
            {
                case GameStateCheckStatus.CheckingLose:
                    UpdateLoseCheck();
                    break;

                case GameStateCheckStatus.CheckingClusters:
                    UpdateClusterCheck();
                    break;

                case GameStateCheckStatus.ClearingClusters:
                    UpdateClusterClearing();
                    break;
            }
        }

        private void StartLoseCheck()
        {
            var pixelBuffer = GetPixelBuffer();
            if (pixelBuffer == null || !pixelBuffer.IsCreated)
                return;

            // Dispose previous job data if exists
            if (_currentJobData.IsValid)
                _currentJobData.Dispose();

            // Initialize job data
            _currentJobData = new GameStateJobData
            {
                hasLost = new NativeArray<bool>(1, Allocator.TempJob),
                firstLosePixelX = new NativeArray<int>(1, Allocator.TempJob),
                firstLosePixelY = new NativeArray<int>(1, Allocator.TempJob)
            };

            var loseCheckJob = new OptimizedLoseCheckJob
            {
                worldWidth = _worldWidth,
                worldHeight = _worldHeight,
                loseThresholdY = _loseThresholdY,
                pixelBuffer = pixelBuffer,
                hasLost = _currentJobData.hasLost,
                firstLosePixelX = _currentJobData.firstLosePixelX,
                firstLosePixelY = _currentJobData.firstLosePixelY
            };

            _currentJobHandle = loseCheckJob.Schedule();
            _checkStatus = GameStateCheckStatus.CheckingLose;
        }

        private void UpdateLoseCheck()
        {
            if (!_currentJobHandle.IsCompleted)
                return;

            _currentJobHandle.Complete();

            if (_currentJobData.hasLost[0])
            {
                _isGameLost = true;
                OLogger.Log($"Game Lost! Sleeping pixel found at ({_currentJobData.firstLosePixelX[0]}, {_currentJobData.firstLosePixelY[0]})");
                OnGameLost?.Invoke();

                // Cleanup and return to idle
                _currentJobData.Dispose();
                _checkStatus = GameStateCheckStatus.Idle;
                return;
            }

            // No lose condition, proceed to cluster check
            StartClusterCheck();
        }

        private void StartClusterCheck()
        {
            var pixelBuffer = GetPixelBuffer();
            if (pixelBuffer == null || !pixelBuffer.IsCreated)
            {
                OLogger.LogWarning("StartClusterCheck: No valid pixel buffer available");
                _checkStatus = GameStateCheckStatus.Idle;
                return;
            }

            // Skip if no materials have been placed yet
            if (_activeMaterialIndices.Count == 0)
            {
                OLogger.LogWarning("StartClusterCheck: No active materials to check");
                _checkStatus = GameStateCheckStatus.Idle;
                return;
            }

            OLogger.Log($"StartClusterCheck: Checking {_activeMaterialIndices.Count} active material indices: [{string.Join(", ", _activeMaterialIndices)}]");
            OLogger.Log($"StartClusterCheck: World size {_worldWidth}x{_worldHeight}, valid X range: {_minValidX}-{_maxValidX}");

            // Convert active material indices to NativeArray
            var activeMaterialIndicesArray = new NativeArray<int>(_activeMaterialIndices.Count, Allocator.TempJob);
            int index = 0;
            foreach (var materialIndex in _activeMaterialIndices)
            {
                activeMaterialIndicesArray[index] = materialIndex;
                OLogger.Log($"Adding active material index to job: {materialIndex}");
                index++;
            }

            // Prepare job data
            int totalPixels = _worldWidth * _worldHeight;
            _currentJobData.clusterMap = new NativeArray<int>(totalPixels, Allocator.TempJob);
            _currentJobData.clusters = new NativeArray<ClusterData>(totalPixels, Allocator.TempJob);
            _currentJobData.clearableClusters = new NativeArray<int>(totalPixels, Allocator.TempJob);
            _currentJobData.clusterCount = new NativeArray<int>(1, Allocator.TempJob);
            _currentJobData.clearableCount = new NativeArray<int>(1, Allocator.TempJob);
            _currentJobData.activeMaterialIndices = activeMaterialIndicesArray; // Now using material indices

            var floodFillJob = new FloodFillJob
            {
                worldWidth = _worldWidth,
                worldHeight = _worldHeight,
                minValidX = _minValidX,
                maxValidX = _maxValidX,
                pixelBuffer = pixelBuffer,
                activeMaterialIndices = _currentJobData.activeMaterialIndices,
                clusterMap = _currentJobData.clusterMap,
                clusters = _currentJobData.clusters,
                clearableClusters = _currentJobData.clearableClusters,
                clusterCount = _currentJobData.clusterCount,
                clearableCount = _currentJobData.clearableCount
            };

            _currentJobHandle = floodFillJob.Schedule();
            _checkStatus = GameStateCheckStatus.CheckingClusters;
        }

        private void UpdateClusterCheck()
        {
            if (!_currentJobHandle.IsCompleted)
                return;

            _currentJobHandle.Complete();

            // Process results
            int foundClusters = _currentJobData.clusterCount[0];
            int clearableClusterCount = _currentJobData.clearableCount[0];

            _activeClusters = foundClusters;

            OLogger.Log($"UpdateClusterCheck: Found {foundClusters} total clusters, {clearableClusterCount} clearable clusters");

            // Debug: Log cluster details
            for (int i = 0; i < foundClusters && i < _currentJobData.clusters.Length; i++)
            {
                var cluster = _currentJobData.clusters[i];
                var materialId = GetMaterialId(cluster.materialIndex);
                OLogger.Log($"  Cluster {i}: MaterialIndex {cluster.materialIndex} (ID: {materialId}), MinX={cluster.minX}, MaxX={cluster.maxX}, SpansLeftToRight={cluster.spansFromLeftToRight}");
            }

            if (clearableClusterCount > 0)
            {
                OLogger.Log($"Starting cluster clearing for {clearableClusterCount} clusters");
                StartClusterClearing(clearableClusterCount);
            }
            else
            {
                OLogger.Log("No clusters to clear, returning to idle");
                // No clusters to clear, cleanup and return to idle
                _currentJobData.Dispose();
                _checkStatus = GameStateCheckStatus.Idle;
            }
        }

        private void StartClusterClearing(int clearableClusterCount)
        {
            // Collect all pixels to animate
            _pixelsToAnimate.Clear();
            var materialIndexCounts = new Dictionary<int, int>(); // Track by material INDEX

            for (int i = 0; i < clearableClusterCount; i++)
            {
                int clusterId = _currentJobData.clearableClusters[i];
                var cluster = _currentJobData.clusters[clusterId];

                // Cluster now stores materialIndex directly
                int materialIndex = cluster.materialIndex;

                // Collect pixels in this cluster
                for (int y = 0; y < _worldHeight; y++)
                {
                    for (int x = 0; x < _worldWidth; x++)
                    {
                        int index = y * _worldWidth + x;
                        if (_currentJobData.clusterMap[index] == clusterId)
                        {
                            _pixelsToAnimate.Add(new Vector2Int(x, y));

                            // Count materials being cleared by INDEX
                            if (!materialIndexCounts.ContainsKey(materialIndex))
                                materialIndexCounts[materialIndex] = 0;
                            materialIndexCounts[materialIndex]++;
                        }
                    }
                }
            }

            if (_pixelsToAnimate.Count > 0)
            {
                OLogger.Log($"Clearing {clearableClusterCount} clusters with {_pixelsToAnimate.Count} pixels");

                // Update material counts
                UpdateMaterialCountsAfterClear(materialIndexCounts);

                // Stop free falling for pixels being animated
                StopFreeFallingForPixels(_pixelsToAnimate);

                // Start animation
                _animationTimer = 0f;
                _checkStatus = GameStateCheckStatus.ClearingClusters;

                OnClustersCleared?.Invoke(clearableClusterCount);
            }
            else
            {
                // No pixels to clear, cleanup and return to idle
                _currentJobData.Dispose();
                _checkStatus = GameStateCheckStatus.Idle;
            }
        }

        private void UpdateClusterClearing()
        {
            _animationTimer += Time.deltaTime;

            // Simple flash animation
            float normalizedTime = _animationTimer / ANIMATION_DURATION;
            bool shouldFlash = (Mathf.FloorToInt(normalizedTime * 6) % 2) == 0; // 3 flashes

            if (shouldFlash)
            {
                SetPixelsColor(_pixelsToAnimate, Color.white);
            }
            else
            {
                SetPixelsToOriginalColor(_pixelsToAnimate);
            }

            // Animation complete
            if (_animationTimer >= ANIMATION_DURATION)
            {
                // Clear all pixels
                ClearPixels(_pixelsToAnimate);

                // Cleanup and return to idle
                _currentJobData.Dispose();
                _checkStatus = GameStateCheckStatus.Idle;
                _pixelsToAnimate.Clear();
            }
        }

        private void StopFreeFallingForPixels(List<Vector2Int> pixels)
        {
            foreach (var pos in pixels)
            {
                if (_sandWorld.PixelWorld.TryGetPixelAt(pos.x, pos.y, out Pixel pixel))
                {
                    pixel.StopFreeFalling();
                    // Update the pixel back to the world
                    _sandWorld.PixelWorld.DrawPixelAt(pos.x, pos.y, pixel.r, pixel.g, pixel.b, pixel.a,
                        (PixelMaterialId)pixel.materialIndex);
                }
            }
        }

        private void SetPixelsColor(List<Vector2Int> pixels, Color32 color)
        {
            foreach (var pos in pixels)
            {
                if (_sandWorld.PixelWorld.TryGetPixelAt(pos.x, pos.y, out Pixel pixel))
                {
                    _sandWorld.PixelWorld.DrawPixelAt(pos.x, pos.y,
                        color.r, color.g, color.b, color.a, (PixelMaterialId)pixel.materialIndex);
                }
            }
        }

        private void SetPixelsToOriginalColor(List<Vector2Int> pixels)
        {
            foreach (var pos in pixels)
            {
                if (_sandWorld.PixelWorld.TryGetPixelAt(pos.x, pos.y, out Pixel pixel))
                {
                    // Get original color from pixel data (this is simplified)
                    _sandWorld.PixelWorld.DrawPixelAt(pos.x, pos.y,
                        pixel.r, pixel.g, pixel.b, pixel.a, (PixelMaterialId)pixel.materialIndex);
                }
            }
        }

        private void ClearPixels(List<Vector2Int> pixels)
        {
            foreach (var pos in pixels)
            {
                _sandWorld.PixelWorld.DrawPixelAt(pos.x, pos.y, PixelMaterialId.Empty);
            }
        }

        private void UpdateMaterialCountsAfterClear(Dictionary<int, int> clearedIndexCounts)
        {
            foreach (var kvp in clearedIndexCounts)
            {
                var materialIndex = kvp.Key;
                var clearedCount = kvp.Value;

                if (_materialIndexPixelCounts.ContainsKey(materialIndex))
                {
                    _materialIndexPixelCounts[materialIndex] -= clearedCount;
                    OLogger.Log($"Material index {materialIndex}: {_materialIndexPixelCounts[materialIndex] + clearedCount} -> {_materialIndexPixelCounts[materialIndex]} pixels");

                    // Remove material from active set if no pixels left
                    if (_materialIndexPixelCounts[materialIndex] <= 0)
                    {
                        _materialIndexPixelCounts.Remove(materialIndex);
                        _activeMaterialIndices.Remove(materialIndex);

                        // Also remove from placed material IDs
                        var materialIdToRemove = GetMaterialId(materialIndex);
                        if (materialIdToRemove != PixelMaterialId.Empty)
                        {
                            _placedMaterialIds.Remove(materialIdToRemove);
                        }

                        OLogger.Log($"Material index {materialIndex} completely cleared from board");
                    }
                }
                else
                {
                    OLogger.LogWarning($"Tried to clear material index {materialIndex} but it's not in pixel counts");
                }
            }
        }

        private NativeArray<Pixel> GetPixelBuffer()
        {
            // This method needs to collect all pixels from the world into a single buffer
            // For the flood fill and lose check algorithms to work properly
            if (_sandWorld?.PixelWorld?.ActiveChunks == null || _sandWorld.PixelWorld.ActiveChunks.Count == 0)
                return default;

            // For single chunk mode (AlwaysShowFullChunk = true), use the chunk directly
            if (_sandWorld.PixelWorld.AlwaysShowFullChunk && _sandWorld.PixelWorld.ActiveChunks.Count > 0)
            {
                var firstChunk = _sandWorld.PixelWorld.ActiveChunks[0];
                if (firstChunk.LoadSucceeded && firstChunk.Pixels.IsCreated)
                {
                    return firstChunk.Pixels;
                }
            }

            // For multi-chunk mode, we would need to combine all chunks into a single buffer
            // This is more complex and would require additional implementation
            OLogger.LogWarning("Multi-chunk mode not fully implemented for game state checking");
            return default;
        }

        /// <summary>
        /// Reset game state
        /// </summary>
        public void ResetGameState()
        {
            _isGameLost = false;
            _activeClusters = 0;
            _clearAnimationSystem?.StopAnimation();

            // Reset material tracking
            _activeMaterialIndices.Clear();
            _materialIndexPixelCounts.Clear();
            _placedMaterialIds.Clear();
            // Don't clear _materialIdToIndexMap as it's built from material definition

            // Cleanup job system safely
            CleanupJobSystem();

            _checkStatus = GameStateCheckStatus.Idle;
            _pixelsToAnimate.Clear();
            _animationTimer = 0f;

            OLogger.Log("Game state and material tracking reset");
        }

        /// <summary>
        /// Safely cleanup job system to prevent memory leaks
        /// </summary>
        private void CleanupJobSystem()
        {
            try
            {
                _currentJobHandle.Complete();
            }
            catch (System.Exception e)
            {
                OLogger.LogError($"Error completing job handle: {e.Message}");
            }

            try
            {
                if (_currentJobData.IsValid)
                {
                    _currentJobData.Dispose();
                }
            }
            catch (System.Exception e)
            {
                OLogger.LogError($"Error disposing job data: {e.Message}");
            }
        }

        /// <summary>
        /// Called when Board is being destroyed
        /// </summary>
        private void OnDestroy()
        {
            CleanupJobSystem();
        }

        /// <summary>
        /// Force check game state (for testing)
        /// </summary>
        [ContextMenu("Force Check Game State")]
        public void ForceCheckGameState()
        {
            OLogger.Log("=== FORCE CHECK GAME STATE ===");
            OLogger.Log($"Current status: {_checkStatus}");
            OLogger.Log($"Active material indices: [{string.Join(", ", _activeMaterialIndices)}]");
            OLogger.Log($"Material index counts: {string.Join(", ", _materialIndexPixelCounts.Select(kvp => $"Index{kvp.Key}:{kvp.Value}"))}");
            OLogger.Log($"Material ID to Index mapping: {string.Join(", ", _materialIdToIndexMap.Select(kvp => $"{kvp.Key}->{kvp.Value}"))}");

            // Force reset to idle and check
            _currentJobHandle.Complete();
            if (_currentJobData.IsValid)
                _currentJobData.Dispose();
            _checkStatus = GameStateCheckStatus.Idle;

            CheckGameState();
        }

        /// <summary>
        /// Debug method to check pixel buffer contents
        /// </summary>
        [ContextMenu("Debug Pixel Buffer")]
        public void DebugPixelBuffer()
        {
            var pixelBuffer = GetPixelBuffer();
            if (!pixelBuffer.IsCreated)
            {
                OLogger.LogWarning("No pixel buffer available");
                return;
            }

            int totalPixels = pixelBuffer.Length;
            int nonEmptyPixels = 0;
            var materialCounts = new Dictionary<int, int>();

            for (int i = 0; i < totalPixels; i++)
            {
                var pixel = pixelBuffer[i];
                if (!pixel.IsEmpty())
                {
                    nonEmptyPixels++;
                    if (!materialCounts.ContainsKey(pixel.materialIndex))
                        materialCounts[pixel.materialIndex] = 0;
                    materialCounts[pixel.materialIndex]++;
                }
            }

            OLogger.Log($"Pixel Buffer Debug: {nonEmptyPixels}/{totalPixels} non-empty pixels");
            OLogger.Log($"Material distribution: {string.Join(", ", materialCounts.Select(kvp => $"Index{kvp.Key}:{kvp.Value}"))}");
        }

        /// <summary>
        /// Debug method to check material definitions
        /// </summary>
        [ContextMenu("Debug Material Definitions")]
        public void DebugMaterialDefinitions()
        {
            OLogger.Log("=== MATERIAL DEFINITIONS DEBUG ===");

            if (_materialDefinition?.Materials != null)
            {
                OLogger.Log($"Material Definition has {_materialDefinition.Materials.Length} materials:");
                for (int i = 0; i < _materialDefinition.Materials.Length; i++)
                {
                    var material = _materialDefinition.Materials[i];
                    OLogger.Log($"  Index {i}: ID={material.id}");
                }
            }
            else
            {
                OLogger.LogError("Material Definition is null or has no materials!");
            }

            OLogger.Log($"ID to Index mapping has {_materialIdToIndexMap.Count} entries:");
            foreach (var kvp in _materialIdToIndexMap)
            {
                OLogger.Log($"  {kvp.Key} -> {kvp.Value}");
            }
        }

        /// <summary>
        /// Test method to simulate cluster clearing
        /// </summary>
        [ContextMenu("Test Cluster Clearing")]
        public void TestClusterClearing()
        {
            OLogger.Log("=== TEST CLUSTER CLEARING ===");

            // Create fake clearable clusters for testing
            _pixelsToAnimate.Clear();

            // Find some sand pixels to animate
            var pixelBuffer = GetPixelBuffer();
            if (!pixelBuffer.IsCreated)
            {
                OLogger.LogWarning("No pixel buffer for testing");
                return;
            }

            for (int y = 0; y < _worldHeight && _pixelsToAnimate.Count < 50; y++)
            {
                for (int x = 0; x < _worldWidth && _pixelsToAnimate.Count < 50; x++)
                {
                    int index = y * _worldWidth + x;
                    var pixel = pixelBuffer[index];
                    if (!pixel.IsEmpty() && _activeMaterialIndices.Contains(pixel.materialIndex))
                    {
                        _pixelsToAnimate.Add(new Vector2Int(x, y));
                    }
                }
            }

            if (_pixelsToAnimate.Count > 0)
            {
                OLogger.Log($"Starting test animation with {_pixelsToAnimate.Count} pixels");
                _animationTimer = 0f;
                _checkStatus = GameStateCheckStatus.ClearingClusters;
                OnClustersCleared?.Invoke(1);
            }
            else
            {
                OLogger.LogWarning("No pixels found for test animation");
            }
        }

        /// <summary>
        /// Check if we should trigger a new game state check
        /// Call this from GameManager when appropriate (e.g., after physics settle)
        /// </summary>
        public bool ShouldCheckGameState()
        {
            // Don't check if game is lost or already checking
            if (_isGameLost || _checkStatus != GameStateCheckStatus.Idle)
                return false;

            // Don't check if there are pending pixels to draw
            if (HasPendingPixelsToDraw())
                return false;

            // Don't check immediately after drawing - wait for pixels to settle
            if (Time.time - _lastDrawTime < DRAW_TO_CHECK_DELAY)
                return false;

            return true;
        }

        /// <summary>
        /// Check if there are pixels waiting to be drawn to the world
        /// This helps prevent race conditions with job system
        /// </summary>
        private bool HasPendingPixelsToDraw()
        {
            // Check if there are pixels waiting to be drawn
            return _sandWorld.PixelWorld._pixelsToDraw.Count > 0 ||
                   _sandWorld.PixelWorld._pixelsToWakeUp.Count > 0;
        }
    }
}