using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Unity.Collections;

namespace OP.BlockSand
{
    /// <summary>
    /// System to handle the clear animation for sand clusters
    /// Changes pixel colors to white, then back to original, then clears them
    /// </summary>
    public class ClearAnimationSystem : MonoBehaviour
    {
        [Header("Animation Settings")]
        [SerializeField] private float animationDuration = 1.0f;
        [SerializeField] private float whiteFlashDuration = 0.2f;
        [SerializeField] private int flashCount = 3;
        [SerializeField] private Color32 flashColor = Color.white;
        
        private PixelWorld _pixelWorld;
        private List<ClearAnimationData> _animatingPixels = new List<ClearAnimationData>();
        private bool _isAnimating = false;
        
        public bool IsAnimating => _isAnimating;
        
        public void Initialize(PixelWorld pixelWorld)
        {
            _pixelWorld = pixelWorld;
        }
        
        /// <summary>
        /// Start clear animation for a cluster of pixels
        /// </summary>
        public void StartClearAnimation(List<Vector2Int> pixelPositions, System.Action onComplete = null)
        {
            if (_isAnimating)
            {
                Debug.LogWarning("Clear animation already in progress!");
                return;
            }
            
            _animatingPixels.Clear();
            
            // Collect pixel data and stop their free falling
            foreach (var pos in pixelPositions)
            {
                if (_pixelWorld.TryGetPixelAt(pos.x, pos.y, out Pixel pixel))
                {
                    // Stop free falling during animation
                    pixel.StopFreeFalling();

                    // Get material ID from material index for drawing
                    var materialId = GetMaterialIdFromIndex(pixel.materialIndex);
                    _pixelWorld.DrawPixelAt(pos.x, pos.y, pixel.r, pixel.g, pixel.b, pixel.a, materialId);

                    var animData = new ClearAnimationData(pos,
                        new Color32(pixel.r, pixel.g, pixel.b, pixel.a),
                        materialId);
                    _animatingPixels.Add(animData);
                }
            }
            
            if (_animatingPixels.Count > 0)
            {
                // StartCoroutine(AnimateClearSequence(onComplete));
                SetPixelsColor(flashColor);
            }
            else
            {
                onComplete?.Invoke();
            }
        }
        
        private IEnumerator AnimateClearSequence(System.Action onComplete)
        {
            _isAnimating = true;
            
            float flashInterval = animationDuration / (flashCount * 2); // *2 for on/off cycles
            
            // Flash animation: alternate between original color and white
            for (int flash = 0; flash < flashCount; flash++)
            {
                // Flash to white
                SetPixelsColor(flashColor);
                yield return new WaitForSeconds(whiteFlashDuration);
                
                // Flash back to original
                SetPixelsToOriginalColor();
                yield return new WaitForSeconds(flashInterval - whiteFlashDuration);
            }
            
            // Final flash to white before clearing
            SetPixelsColor(flashColor);
            yield return new WaitForSeconds(whiteFlashDuration);
            
            // Clear all pixels
            ClearAllAnimatingPixels();
            
            _isAnimating = false;
            _animatingPixels.Clear();
            
            onComplete?.Invoke();
        }
        
        private void SetPixelsColor(Color32 color)
        {
            foreach (var animData in _animatingPixels)
            {
                _pixelWorld.DrawPixelAt(animData.position.x, animData.position.y, 
                    color.r, color.g, color.b, color.a, animData.materialId);
            }
        }
        
        private void SetPixelsToOriginalColor()
        {
            foreach (var animData in _animatingPixels)
            {
                var originalColor = animData.originalColor;
                _pixelWorld.DrawPixelAt(animData.position.x, animData.position.y, 
                    originalColor.r, originalColor.g, originalColor.b, originalColor.a, 
                    animData.materialId);
            }
        }
        
        private void ClearAllAnimatingPixels()
        {
            foreach (var animData in _animatingPixels)
            {
                _pixelWorld.DrawPixelAt(animData.position.x, animData.position.y, 
                    PixelMaterialId.Empty);
            }
        }
        
        /// <summary>
        /// Stop current animation and clear all pixels immediately
        /// </summary>
        public void StopAnimation()
        {
            if (_isAnimating)
            {
                StopAllCoroutines();
                // ClearAllAnimatingPixels();
                _isAnimating = false;
                _animatingPixels.Clear();
            }
        }
        
        /// <summary>
        /// Get all pixel positions currently being animated
        /// </summary>
        public List<Vector2Int> GetAnimatingPixelPositions()
        {
            var positions = new List<Vector2Int>();
            foreach (var animData in _animatingPixels)
            {
                positions.Add(animData.position);
            }
            return positions;
        }

        /// <summary>
        /// Convert material index to material ID using the pixel world's materials
        /// </summary>
        private PixelMaterialId GetMaterialIdFromIndex(int materialIndex)
        {
            var levelInfo = LevelInfo.GetLoadedLevelInfo(_pixelWorld.Level);
            if (levelInfo?.Materials?.Materials != null && materialIndex >= 0 && materialIndex < levelInfo.Materials.Materials.Length)
            {
                return levelInfo.Materials.Materials[materialIndex].id;
            }
            return PixelMaterialId.Empty;
        }
    }
}
