using System.Collections.Generic;
using Unity.Collections;
using Unity.Jobs;
using UnityEngine;

namespace OP.BlockSand
{
    /// <summary>
    /// Status of game state checking process
    /// </summary>
    public enum GameStateCheckStatus
    {
        Idle,
        CheckingLose,
        CheckingClusters,
        ClearingClusters,
        Completed
    }

    /// <summary>
    /// Data container for async job operations
    ///
    /// Note: clusterCount và clearableCount phải là NativeArray vì Unity Job System
    /// yêu cầu tất cả output data từ job phải là NativeArray để có thể write thread-safe.
    /// Không thể sử dụng ref int hoặc out int trong jobs.
    /// </summary>
    public struct GameStateJobData
    {
        // Lose check data
        public NativeArray<bool> hasLost;           // [0] = has lost
        public NativeArray<int> firstLosePixelX;   // [0] = first lose pixel X
        public NativeArray<int> firstLosePixelY;   // [0] = first lose pixel Y

        // Flood fill data
        public NativeArray<int> clusterMap;        // Maps pixel index to cluster ID
        public NativeArray<ClusterData> clusters;  // Array of cluster data
        public NativeArray<int> clearableClusters; // List of clearable cluster IDs
        public NativeArray<int> clusterCount;      // [0] = total clusters found
        public NativeArray<int> clearableCount;    // [0] = clearable clusters count
        public NativeArray<int> activeMaterialIndices; // List of active material IDs to check

        public bool IsValid => hasLost.IsCreated;

        public void Dispose()
        {
            if (hasLost.IsCreated) hasLost.Dispose();
            if (firstLosePixelX.IsCreated) firstLosePixelX.Dispose();
            if (firstLosePixelY.IsCreated) firstLosePixelY.Dispose();
            if (clusterMap.IsCreated) clusterMap.Dispose();
            if (clusters.IsCreated) clusters.Dispose();
            if (clearableClusters.IsCreated) clearableClusters.Dispose();
            if (clusterCount.IsCreated) clusterCount.Dispose();
            if (clearableCount.IsCreated) clearableCount.Dispose();
            if (activeMaterialIndices.IsCreated) activeMaterialIndices.Dispose();
        }
    }
    /// <summary>
    /// Data structure to hold information about a sand cluster
    /// </summary>
    public struct ClusterData
    {
        public int clusterId;
        public int materialIndex; // Store material index instead of material ID
        public bool spansFromLeftToRight;
        public int pixelCount;
        public int minX;
        public int maxX;
        public int minY;
        public int maxY;

        public ClusterData(int id, int materialIdx)
        {
            clusterId = id;
            materialIndex = materialIdx;
            spansFromLeftToRight = false;
            pixelCount = 0;
            minX = int.MaxValue;
            maxX = int.MinValue;
            minY = int.MaxValue;
            maxY = int.MinValue;
        }
        
        public void AddPixel(int x, int y)
        {
            pixelCount++;
            minX = Mathf.Min(minX, x);
            maxX = Mathf.Max(maxX, x);
            minY = Mathf.Min(minY, y);
            maxY = Mathf.Max(maxY, y);
        }
        
        public void CheckSpansFromLeftToRight(int worldMinX, int worldMaxX)
        {
            // Check if cluster spans from left boundary to right boundary
            // A cluster spans if it touches or is very close to both boundaries
            // worldMinX = 1 (after left boundary), worldMaxX = worldWidth - 2 (before right boundary)
            spansFromLeftToRight = minX <= worldMinX && maxX >= worldMaxX;
        }
    }
    
    /// <summary>
    /// Animation data for clearing pixels
    /// </summary>
    public struct ClearAnimationData
    {
        public Vector2Int position;
        public Color32 originalColor;
        public PixelMaterialId materialId;
        public float animationTime;
        public bool isAnimating;
        
        public ClearAnimationData(Vector2Int pos, Color32 color, PixelMaterialId material)
        {
            position = pos;
            originalColor = color;
            materialId = material;
            animationTime = 0f;
            isAnimating = true;
        }
    }
}
