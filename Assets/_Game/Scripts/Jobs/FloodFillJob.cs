using Unity.Burst;
using Unity.Collections;
using Unity.Jobs;
using Unity.Mathematics;
using UnityEngine;

namespace OP.BlockSand
{
    /// <summary>
    /// Job to perform flood fill algorithm to find sand clusters that span from left to right
    /// </summary>
    // Temporarily disable <PERSON><PERSON><PERSON> for debugging - enable this for production
    // [BurstCompile]
    public struct FloodFillJob : IJob
    {
        // Input data
        [ReadOnly] public int worldWidth;
        [ReadOnly] public int worldHeight;
        [ReadOnly] public int minValidX; // Should be 1 (after left boundary)
        [ReadOnly] public int maxValidX; // Should be 84 (before right boundary)
        [ReadOnly] public NativeArray<Pixel> pixelBuffer;
        [ReadOnly] public NativeArray<int> activeMaterialIndices; // Only check these material indices (not IDs)
        
        // Output data
        public NativeArray<int> clusterMap; // Maps pixel index to cluster ID
        public NativeArray<ClusterData> clusters;
        public NativeArray<int> clearableClusters; // List of cluster IDs that can be cleared
        public NativeArray<int> clusterCount;
        public NativeArray<int> clearableCount;
        
        public void Execute()
        {
            // Initialize
            var visited = new NativeArray<bool>(pixelBuffer.Length, Allocator.Temp);
            var stack = new NativeArray<int2>(pixelBuffer.Length, Allocator.Temp);

            int currentClusterId = 0;
            int clearableClusterCount = 0;
            int totalValidPixels = 0;

            // Count valid pixels for debugging
            int totalNonEmptyPixels = 0;
            int totalActivePixels = 0;

            for (int i = 0; i < pixelBuffer.Length; i++)
            {
                var pixel = pixelBuffer[i];
                if (!pixel.IsEmpty())
                {
                    totalNonEmptyPixels++;
                    if (IsActiveMaterial(pixel.materialIndex))
                        totalActivePixels++;
                }

                if (IsValidSandPixel(i))
                    totalValidPixels++;
            }

            // Debug output (works when Burst is disabled)
            UnityEngine.Debug.Log($"FloodFillJob Debug: {totalNonEmptyPixels} non-empty, {totalActivePixels} active material, {totalValidPixels} valid pixels");
            UnityEngine.Debug.Log($"Active materials count: {activeMaterialIndices.Length}");
            for (int i = 0; i < activeMaterialIndices.Length; i++)
            {
                UnityEngine.Debug.Log($"  Active material {i}: {activeMaterialIndices[i]}");
            }

            // Iterate through all pixels
            for (int y = 0; y < worldHeight; y++)
            {
                for (int x = 0; x < worldWidth; x++)
                {
                    int index = GetIndex(x, y);

                    if (visited[index] || !IsValidSandPixel(index))
                        continue;

                    // Start flood fill for this cluster
                    var pixel = pixelBuffer[index];
                    var cluster = new ClusterData(currentClusterId, (PixelMaterialId)pixel.materialIndex);
                    int pixelsInCluster = FloodFillCluster(x, y, visited, stack, ref cluster);

                    if (pixelsInCluster > 0)
                    {
                        cluster.CheckSpansFromLeftToRight(minValidX, maxValidX);
                        clusters[currentClusterId] = cluster;

                        if (cluster.spansFromLeftToRight)
                        {
                            clearableClusters[clearableClusterCount] = currentClusterId;
                            clearableClusterCount++;
                        }

                        currentClusterId++;
                    }
                }
            }
            
            clusterCount[0] = currentClusterId;
            clearableCount[0] = clearableClusterCount;

            // Debug output (works when Burst is disabled)
            UnityEngine.Debug.Log($"FloodFillJob: Found {totalValidPixels} valid pixels, {currentClusterId} clusters, {clearableClusterCount} clearable");
            for (int i = 0; i < currentClusterId && i < clusters.Length; i++)
            {
                var cluster = clusters[i];
                UnityEngine.Debug.Log($"  Cluster {i}: Material {cluster.materialId}, Pixels={cluster.pixelCount}, X={cluster.minX}-{cluster.maxX}, Spans={cluster.spansFromLeftToRight}");
            }

            visited.Dispose();
            stack.Dispose();
        }
        
        private int FloodFillCluster(int startX, int startY, NativeArray<bool> visited,
            NativeArray<int2> stack, ref ClusterData cluster)
        {
            int stackTop = 0;
            int pixelCount = 0;
            var targetMaterialIndex = pixelBuffer[GetIndex(startX, startY)].materialIndex;
            
            // Add starting pixel to stack
            stack[stackTop] = new int2(startX, startY);
            stackTop++;
            
            while (stackTop > 0)
            {
                // Pop from stack
                stackTop--;
                var current = stack[stackTop];
                int x = current.x;
                int y = current.y;
                int index = GetIndex(x, y);
                
                // Skip if already visited or invalid
                if (visited[index] || !IsValidSandPixel(index) ||
                    pixelBuffer[index].materialIndex != targetMaterialIndex)
                    continue;
                
                // Mark as visited and assign to cluster
                visited[index] = true;
                clusterMap[index] = cluster.clusterId;
                cluster.AddPixel(x, y);
                pixelCount++;
                
                // Add neighbors to stack (8-directional including diagonals)
                AddNeighborToStack(x - 1, y, stack, ref stackTop);     // Left
                AddNeighborToStack(x + 1, y, stack, ref stackTop);     // Right
                AddNeighborToStack(x, y - 1, stack, ref stackTop);     // Down
                AddNeighborToStack(x, y + 1, stack, ref stackTop);     // Up
                AddNeighborToStack(x - 1, y - 1, stack, ref stackTop); // Bottom-left
                AddNeighborToStack(x + 1, y - 1, stack, ref stackTop); // Bottom-right
                AddNeighborToStack(x - 1, y + 1, stack, ref stackTop); // Top-left
                AddNeighborToStack(x + 1, y + 1, stack, ref stackTop); // Top-right
            }
            
            return pixelCount;
        }
        
        private void AddNeighborToStack(int x, int y, NativeArray<int2> stack, ref int stackTop)
        {
            if (IsValidCoordinate(x, y) && stackTop < stack.Length)
            {
                stack[stackTop] = new int2(x, y);
                stackTop++;
            }
        }
        
        private bool IsValidCoordinate(int x, int y)
        {
            return x >= 0 && x < worldWidth && y >= 0 && y < worldHeight;
        }
        
        private int GetIndex(int x, int y)
        {
            return y * worldWidth + x;
        }
        
        private bool IsValidSandPixel(int index)
        {
            var pixel = pixelBuffer[index];
            if (pixel.IsEmpty())
                return false;

            // Don't require pixels to be asleep - they might still be settling
            // if (!pixel.IsAsleep())
            //     return false;

            // Check if this material index is in our active materials list
            bool isActive = IsActiveMaterial(pixel.materialIndex);

            // Debug first few pixels to understand the issue
            if (index < 10 && !pixel.IsEmpty())
            {
                UnityEngine.Debug.Log($"Pixel {index}: materialIndex={pixel.materialIndex}, isActive={isActive}");
            }

            return isActive;
        }

        private bool IsActiveMaterial(int materialIndex)
        {
            // Check if this material index is in our active materials array
            for (int i = 0; i < activeMaterialIndices.Length; i++)
            {
                if (activeMaterialIndices[i] == materialIndex)
                    return true;
            }
            return false;
        }

    }
}
