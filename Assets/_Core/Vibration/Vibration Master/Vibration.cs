using UnityEngine;
namespace OnePuz.Vibration_Master
{
#if UNITY_IOS
using System.Collections;
using System.Runtime.InteropServices;
#endif

    public static class Vibration
    {

#if UNITY_IOS
    [DllImport ( "__Internal" )]
    private static extern bool _HasVibrator ();

    [DllImport ( "__Internal" )]
    private static extern void _Vibrate ();

    [DllImport ( "__Internal" )]
    private static extern void _VibratePop ();

    [DllImport ( "__Internal" )]
    private static extern void _VibratePeek ();

    [DllImport ( "__Internal" )]
    private static extern void _VibrateNope ();

    [DllImport("__Internal")]
    private static extern void _impactOccurred(string style);

    [DllImport("__Internal")]
    private static extern void _notificationOccurred(string style);

    [DllImport("__Internal")]
    private static extern void _selectionChanged();
#endif

#if UNITY_ANDROID
        private static AndroidJavaClass unityPlayer;
        private static AndroidJavaObject vibrator;
        private static AndroidJavaClass vibrationEffect;
        private static bool m_HasVibration;
        private static int m_ApiLevel = 1;
        private static int m_DefaultAmplitude = 255;
#endif

        private static bool initialized;

        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.BeforeSceneLoad)]
        public static void Initialize()
        {
            Debug.Log("<color=lime>Initialize Vibration</color>");
            if (initialized) return;

#if UNITY_ANDROID

            if (Application.isMobilePlatform)
            {
                using (var androidVersionClass = new AndroidJavaClass("android.os.Build$VERSION"))
                {
                    m_ApiLevel = androidVersionClass.GetStatic<int>("SDK_INT");
                }

                unityPlayer = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
                using (var currentActivity = unityPlayer.GetStatic<AndroidJavaObject>("currentActivity"))
                {
                    if (currentActivity != null)
                    {
                        vibrator = currentActivity.Call<AndroidJavaObject>("getSystemService", "vibrator");
                        m_HasVibration = vibrator != null && vibrator.Call<bool>("hasVibrator");
                        if (m_ApiLevel >= 26)
                        {
                            vibrationEffect = new AndroidJavaClass("android.os.VibrationEffect");
                            m_DefaultAmplitude = Mathf.Clamp(vibrationEffect.GetStatic<int>("DEFAULT_AMPLITUDE"), 1, 255);
                        }
                    }
                }

            }
#elif UNITY_IOS
            m_HasVibration = _HasVibrator ();
#endif
            initialized = true;
        }


        public static void VibrateIOS(ImpactFeedbackStyle style)
        {
#if UNITY_IOS
        _impactOccurred(style.ToString());
#endif
        }

        public static void VibrateIOS(NotificationFeedbackStyle style)
        {
#if UNITY_IOS
        _notificationOccurred(style.ToString());
#endif
        }

        public static void VibrateIOS_SelectionChanged()

        {
#if UNITY_IOS
        _selectionChanged();
#endif
        }




        ///<summary>
        /// Tiny pop vibration
        ///</summary>
        public static void VibratePop()
        {
            if(!m_HasVibration)
                return;
            if (Application.isMobilePlatform)
            {
#if UNITY_IOS
        _VibratePop ();
#elif UNITY_ANDROID
                VibrateAndroid(50);
#endif
            }
        }

        ///<summary>
        /// Small peek vibration
        ///</summary>
        public static void VibratePeek()
        {
            if(!m_HasVibration)
                return;
            if (Application.isMobilePlatform)
            {
#if UNITY_IOS
        _VibratePeek ();
#elif UNITY_ANDROID
                VibrateAndroid(100);
#endif
            }
        }

        ///<summary>
        /// 3 small vibrations
        ///</summary>
        public static void VibrateNope()
        {
            if(!m_HasVibration)
                return;
            if (Application.isMobilePlatform)
            {
#if UNITY_IOS
        _VibrateNope ();
#elif UNITY_ANDROID
                long[] pattern = { 0, 50, 50, 50 };
                int[] amplitudes = { 1, m_DefaultAmplitude, m_DefaultAmplitude, m_DefaultAmplitude };
                VibrateAndroid(pattern, amplitudes,  -1);
#endif
            }
        }


#if UNITY_ANDROID
        ///<summary>
        /// Only on Android
        /// https://developer.android.com/reference/android/os/Vibrator.html#vibrate(long)
        ///</summary>
        public static void VibrateAndroid(long milliseconds, int amplitude = -1)
        {

            if (Application.isMobilePlatform)
            {
                if (m_ApiLevel >= 26)
                {
                    if (amplitude <= 0)
                        amplitude = m_DefaultAmplitude;
                    var createOneShot =
                        vibrationEffect.CallStatic<AndroidJavaObject>("createOneShot", milliseconds, amplitude);
                    vibrator.Call("vibrate", createOneShot);

                }
                else
                {
                    vibrator.Call("vibrate", milliseconds);
                }
            }
        }

        ///<summary>
        /// Only on Android
        /// https://proandroiddev.com/using-vibrate-in-android-b0e3ef5d5e07
        ///</summary>
        public static void VibrateAndroid(long[] pattern, int[] amplitudes, int repeat)
        {
            if(!m_HasVibration)
                return;
            if (Application.isMobilePlatform)
            {
                if (m_ApiLevel >= 26)
                {
                    for (var index = 0; index < amplitudes.Length; index++)
                        amplitudes[index] = Mathf.Clamp(amplitudes[index], 1, 255);
                    var createWaveform =
                        vibrationEffect.CallStatic<AndroidJavaObject>("createWaveform", pattern, amplitudes, repeat);
                    vibrator.Call("vibrate", createWaveform);

                }
                else
                {
                    vibrator.Call("vibrate", pattern, repeat);
                }
            }
        }
#endif

        ///<summary>
        ///Only on Android
        ///</summary>
        public static void CancelAndroid()
        {
            if (Application.isMobilePlatform)
            {
#if UNITY_ANDROID
                vibrator.Call("cancel");
#endif
            }
        }

        public static void Vibrate()
        {
#if UNITY_ANDROID || UNITY_IOS

            if (Application.isMobilePlatform)
            {
                Handheld.Vibrate();
            }

#endif
        }
    }

    public enum ImpactFeedbackStyle
    {
        Heavy,
        Medium,
        Light,
        Rigid,
        Soft
    }

    public enum NotificationFeedbackStyle
    {
        Error,
        Success,
        Warning
    }
}