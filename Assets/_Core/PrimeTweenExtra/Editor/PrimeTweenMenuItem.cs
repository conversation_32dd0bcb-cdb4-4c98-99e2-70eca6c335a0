using System;
using UnityEditor;
using UnityEditor.Build;

namespace _FeatureHub.PrimeTweenExtra.Editor
{
    public static class PrimeTweenMenuItem
    {
        private const string MenuPrimeTweenDisableAssertions = "Tools/✧ PrimeTween/Disable Assertions";
        private const string MenuPrimeTweenSafetyChecks = "Tools/✧ PrimeTween/Safety Checks";

        private static bool isDisableAssertions
        {
            get => EditorPrefs.GetBool("is_prime_tween_disable_assertions");
            set => EditorPrefs.SetBool("is_prime_tween_disable_assertions", value);
        }

        private static bool isSafetyChecks
        {
            get => EditorPrefs.GetBool("is_prime_tween_safety_checks");
            set => EditorPrefs.SetBool("is_prime_tween_safety_checks", value);
        }

        [InitializeOnLoadMethod]
        private static void OnPreload()
        {
            if (isDisableAssertions)
                AddDefineSymbol("PRIME_TWEEN_DISABLE_ASSERTIONS");
            else
                RemoveDefineSymbol("PRIME_TWEEN_DISABLE_ASSERTIONS");

            if (isSafetyChecks)
                AddDefineSymbol("PRIME_TWEEN_SAFETY_CHECKS");
            else
                RemoveDefineSymbol("PRIME_TWEEN_SAFETY_CHECKS");
        }

        [MenuItem(MenuPrimeTweenDisableAssertions)]
        public static void TogglePrimeTweenAssertions()
        {
            isDisableAssertions = !isDisableAssertions;
            Menu.SetChecked(MenuPrimeTweenDisableAssertions, isDisableAssertions);

            if (isDisableAssertions)
                AddDefineSymbol("PRIME_TWEEN_DISABLE_ASSERTIONS");
            else
                RemoveDefineSymbol("PRIME_TWEEN_DISABLE_ASSERTIONS");
        }

        [MenuItem(MenuPrimeTweenDisableAssertions, true)]
        public static bool TogglePrimeTweenAssertions_Validate()
        {
            Menu.SetChecked(MenuPrimeTweenDisableAssertions, isDisableAssertions);
            return true;
        }

        [MenuItem(MenuPrimeTweenSafetyChecks)]
        public static void TogglePrimeTweenSafetyChecks()
        {
            isSafetyChecks = !isSafetyChecks;
            Menu.SetChecked(MenuPrimeTweenSafetyChecks, isSafetyChecks);

            if (isSafetyChecks)
                AddDefineSymbol("PRIME_TWEEN_SAFETY_CHECKS");
            else
                RemoveDefineSymbol("PRIME_TWEEN_SAFETY_CHECKS");
        }

        [MenuItem(MenuPrimeTweenSafetyChecks, true)]
        public static bool TogglePrimeTweenSafetyChecks_Validate()
        {
            Menu.SetChecked(MenuPrimeTweenSafetyChecks, isSafetyChecks);
            return true;
        }

        private static void AddDefineSymbol(string defineSymbol)
        {
#if UNITY_2021_3_OR_NEWER
            PlayerSettings.GetScriptingDefineSymbols(NamedBuildTarget.Android, out var currentDefines);
            if (ArrayUtility.Contains(currentDefines, defineSymbol))
                return;
            ArrayUtility.Add(ref currentDefines, defineSymbol);
            PlayerSettings.SetScriptingDefineSymbols(NamedBuildTarget.Android, currentDefines);
#else
            var currentDefines = PlayerSettings.GetScriptingDefineSymbolsForGroup(BuildTargetGroup.Android);
            if (currentDefines.Contains(defineSymbol))
                return;
            currentDefines = currentDefines.Insert(currentDefines.Length, $";{defineSymbol}");
            PlayerSettings.SetScriptingDefineSymbolsForGroup(BuildTargetGroup.Android, currentDefines);
#endif
            AssetDatabase.Refresh();
        }

        private static void RemoveDefineSymbol(string defineSymbol)
        {
#if UNITY_2021_3_OR_NEWER
            PlayerSettings.GetScriptingDefineSymbols(NamedBuildTarget.Android, out var currentDefines);
            if (!ArrayUtility.Contains(currentDefines, defineSymbol))
                return;
            ArrayUtility.Remove(ref currentDefines, defineSymbol);
            PlayerSettings.SetScriptingDefineSymbols(NamedBuildTarget.Android, currentDefines);
#else
            var currentDefines = PlayerSettings.GetScriptingDefineSymbolsForGroup(BuildTargetGroup.Android);
            if (!currentDefines.Contains(defineSymbol))
                return;
            var indexOfSymbol = currentDefines.IndexOf(defineSymbol, StringComparison.Ordinal);
            currentDefines = indexOfSymbol > 0
                ? currentDefines.Remove(indexOfSymbol - 1, defineSymbol.Length + 1)
                : currentDefines.Remove(indexOfSymbol, defineSymbol.Length + 1);
            PlayerSettings.SetScriptingDefineSymbolsForGroup(BuildTargetGroup.Android, currentDefines);
#endif
            AssetDatabase.Refresh();
        }
    }
}