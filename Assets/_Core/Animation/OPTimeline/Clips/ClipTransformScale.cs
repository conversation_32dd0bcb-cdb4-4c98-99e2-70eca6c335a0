using UnityEngine;

namespace OnePuz.OPTimeline
{
    [System.Serializable]
    [OPClipCreate("Transform/Scale", "Animate Scale")]
    public class ClipTransformScale : OPTweenClip<Vector3,Transform>
    {
        protected override Vector3 GetCurrentValue()
        {
            return target.localScale;
        }

        protected override void SetValue(Vector3 newValue)
        {
            target.localScale = newValue;
        }
        
        protected override void OnUpdate(OPEvaluateState state, float timelineTime, float clipTime,
            float normalizedClipTime, bool previewMode)
        {
            if(state != OPEvaluateState.Running) return;
            SetValue(Vector3.LerpUnclamped(CurrentFrom, to, ease.Lerp(normalizedClipTime)));
        }
    }
}