using UnityEngine;

namespace OnePuz.OPTimeline
{
    [System.Serializable]
    [OPClipCreate("Camera/Background Color Punch", "Camera Background Color Punch")]
    public class ClipCameraBackgroundColorPunch : OPPunchCore<Color,Camera>
    {
        protected override Color GetCurrentValue()
        {
            return target.backgroundColor;
        }

        protected override void SetValue(Color newValue)
        {
            target.backgroundColor = newValue;
        }
        
        protected override void OnUpdate(OPEvaluateState state, float timelineTime, float clipTime,
            float normalizedClipTime, bool previewMode)
        {
            if(state != OPEvaluateState.Running) return;
            var damper = 1f - Mathf.Clamp(2f * normalizedClipTime - 1f, 0f, 1f);
            var bounce = Mathf.Sin(normalizedClipTime * Mathf.PI * vibrato) * elasticity;
            var newPosition = punchStrength * (damper * bounce);
            SetValue(startValue + newPosition);
        }
    }
}