using UnityEngine;

namespace OnePuz.OPTimeline
{
    [System.Serializable]
    [OPClipCreate("Light/Light Intensity", "Animate Light Intensity")]
    public class ClipLightIntensity : OPTweenClip<float,Light>
    {
        protected override float GetCurrentValue()
        {
            return target.intensity;
        }

        protected override void SetValue(float newValue)
        {
            target.intensity = newValue;
        }
        
        protected override void OnUpdate(OPEvaluateState state, float timelineTime, float clipTime,
            float normalizedClipTime, bool previewMode)
        {
            if(state != OPEvaluateState.Running) return;
            SetValue(Mathf.LerpUnclamped(CurrentFrom, to, ease.Lerp(normalizedClipTime)));
        }
    }
}