using System;
using System.Collections.Generic;
using UnityEngine;

namespace OnePuz.Audio
{
    public static class AudioPooler
    {
        public struct AudioPool
        {
            private readonly AudioType _audioType;
            private readonly Stack<AudioInstance> _inactive;

            public AudioPool(AudioType audioType)
            {
                _audioType = audioType;
                _inactive = new Stack<AudioInstance>();
            }

            public AudioInstance Spawn(AudioClip clip, bool loop = false, float pitch = 1)
            {
                AudioInstance audio;
                if (_inactive.Count == 0)
                {
                    var source = Core.Audio.pHolder.AddComponent<AudioSource>();
                    audio = new AudioInstance().SetPooler(this).SetAudioSource(source);
                    switch (_audioType)
                    {
                        case AudioType.MUSIC:
                            audio.SetOutput(Core.Audio.pMixer.FindMatchingGroups(AudioService.MUSIC)[0]);
                            break;
                        case AudioType.SOUND_FX:
                            audio.SetOutput(Core.Audio.pMixer.FindMatchingGroups(AudioService.SFX)[0]);
                            break;
                        default:
                            throw new ArgumentOutOfRangeException();
                    }
                }
                else
                {
                    audio = _inactive.Pop();
                }

                audio.SetClip(clip, loop, pitch);
                return audio;
            }

            public void Despawn(AudioInstance audio)
            {
                audio.pAudioSource.enabled = false;
                _inactive.Push(audio);
            }
        }

        private static readonly Dictionary<AudioType, AudioPool> _pools = new ();

        private static void Init(AudioType audioType)
        {
            if (!_pools.ContainsKey(audioType))
                _pools.Add(audioType, new AudioPool(audioType));
        }

        public static AudioInstance Spawn(AudioType audioType, AudioClip clip)
        {
            Init(audioType);
            return _pools[audioType].Spawn(clip);
        }

        public static AudioInstance Spawn(AudioType audioType, AudioClip clip, bool loop, float pitch = 1)
        {
            Init(audioType);
            return _pools[audioType].Spawn(clip, loop, pitch);
        }

        public static AudioInstance Spawn(AudioType audioType, AudioClip clip, float pitch)
        {
            Init(audioType);
            return _pools[audioType].Spawn(clip, false, pitch);
        }

        public static void Despawn(AudioInstance audio)
        {
            if (audio.pAudioSource.enabled)
            {
                audio.pMyPool.Despawn(audio);
            }
        }
    }
}