using System.Collections.Generic;
using System.Reflection;
using UnityEditor;
using UnityEngine;

namespace OnePuz.UI
{
    [CustomPropertyDrawer(typeof(StaticStringFieldSelectorAttribute))]
    public class StaticStringFieldSelectorDrawer : PropertyDrawer
    {
        private bool _displayWarningField = false;
        private int _newFieldIndex = -1;

        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            EditorGUI.BeginProperty(position, label, property);

            // Add space at the top
            position.y += 5f;

            // Calculate rect for configuration
            Rect warningRect = new(position.x, position.y, position.width, EditorGUIUtility.singleLineHeight);
            Rect popupRect = new(position.x, position.y + (_displayWarningField ? 1 * EditorGUIUtility.singleLineHeight : 0f), position.width, EditorGUIUtility.singleLineHeight);

            StaticStringFieldSelectorAttribute selectorAttribute = (StaticStringFieldSelectorAttribute)attribute;
            System.Type targetType = selectorAttribute.TargetType;

            if (property.propertyType != SerializedPropertyType.String)
            {
                EditorGUI.LabelField(position, label.text, "Use StaticStringFieldSelector with string.");
                return;
            }

            List<string> options = new List<string>();
            FieldInfo[] fields = targetType.GetFields(BindingFlags.Static | BindingFlags.Public | BindingFlags.NonPublic);

            foreach (FieldInfo field in fields)
            {
                if (field.FieldType == typeof(string))
                {
                    options.Add((string)field.GetValue(null));
                }
            }

            int currentIndex = options.IndexOf(property.stringValue);
            if (currentIndex < 0)
            {
                // Create a GUIStyle for the warning label
                GUIStyle warningStyle = new GUIStyle(EditorStyles.label);
                warningStyle.richText = true;

                // Temporarily disable the GUI to make the text field non-editable
                GUI.enabled = false;
                EditorGUI.TextField(warningRect, label.text, $"<color=yellow>WARNING:</color> <color=green>{property.stringValue}</color> not found in options", warningStyle);
                GUI.enabled = true;

                int selectedIndex = EditorGUI.Popup(popupRect, "Select new value", 0, options.ToArray());
                if (selectedIndex > 0 && _newFieldIndex != selectedIndex)
                {
                    _newFieldIndex = selectedIndex;
                    property.stringValue = options[selectedIndex];
                }

                _displayWarningField = true;
            }
            else
            {
                int selectedIndex = Mathf.Max(0, currentIndex);
                selectedIndex = EditorGUI.Popup(popupRect, label.text, selectedIndex, options.ToArray());

                if (selectedIndex >= 0 && selectedIndex < options.Count)
                {
                    property.stringValue = options[selectedIndex];
                }

                _displayWarningField = false;
            }

            if (_displayWarningField)
            {
                // Draw outline around the property drawer
                Rect outlineRect = new(position.x - 2, position.y - 2, position.width + 4, position.height - 5);
                Handles.DrawSolidRectangleWithOutline(outlineRect, Color.clear, new Color(0.5f, 0.5f, 0.5f, 0.3f));
            }

            EditorGUI.EndProperty();
        }

        public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
        {
            // Adjust the height to fit the labels and popup
            return _displayWarningField ? (EditorGUIUtility.singleLineHeight * 2f + 15f) : EditorGUIUtility.singleLineHeight + 5f;
        }
    }
}