using System;

namespace _FeatureHub.Utilities
{
    
    /// <summary>
    /// Execute Delegate 0 Allocated Memory
    /// </summary>
    public class CallbackWrapper
    {
        private object m_Target;
        private object m_Callback;
        private event Action<CallbackWrapper> m_CallbackListener;

        public void Add<T>(T target, Action<T> callback) where T : class
        {
            m_Target = target;
            m_Callback = callback;
            m_CallbackListener = null;
            m_CallbackListener += @this => (@this.m_Callback as Action<T>)?.Invoke(@this.m_Target as T);
        }

        public void Release()
        {
            m_CallbackListener = null;
            m_Callback = null;
            m_Target = null;
        }

        public CallbackWrapper Execute()
        {
            m_CallbackListener?.Invoke(this);
            return this;
        }
    }

    /// <summary>
    /// Execute Delegate 0 Allocated Memory
    /// </summary>
    /// <typeparam name="T0">Argument returned</typeparam>
    public class CallbackWrapper<T0>
    {
        private object m_Target;
        private object m_Callback;
        private event Action<CallbackWrapper<T0>, T0> m_CallbackListener;

        public void Add<T>(T target, Action<T, T0> callback) where T : class
        {
            m_Target = target;
            m_Callback = callback;
            m_CallbackListener = null;
            m_CallbackListener += (@this, arg0) 
                => (@this.m_Callback as Action<T, T0>)?.Invoke(@this.m_Target as T, arg0);
        }

        public void Release()
        {
            m_CallbackListener = null;
            m_Callback = null;
            m_Target = null;
        }

        public CallbackWrapper<T0> Execute(T0 arg0)
        {
            m_CallbackListener?.Invoke(this, arg0);
            return this;
        }
    }
    
    /// <summary>
    /// Execute Delegate 0 Allocated Memory
    /// </summary>
    /// <typeparam name="T0">Argument returned</typeparam>
    /// <typeparam name="T1">Argument returned</typeparam>
    public class CallbackWrapper<T0, T1>
    {
        private object m_Target;
        private object m_Callback;
        private event Action<CallbackWrapper<T0, T1>, T0, T1> m_CallbackListener;

        public void Add<T>(T target, Action<T, T0, T1> callback) where T : class
        {
            m_Target = target;
            m_Callback = callback;
            m_CallbackListener = null;
            m_CallbackListener += (@this, arg0, arg1) 
                => (@this.m_Callback as Action<T, T0, T1>)?.Invoke(@this.m_Target as T, arg0, arg1);
        }

        public void Release()
        {
            m_CallbackListener = null;
            m_Callback = null;
            m_Target = null;
        }

        public CallbackWrapper<T0, T1> Execute(T0 arg0, T1 arg1)
        {
            m_CallbackListener?.Invoke(this, arg0, arg1);
            return this;
        }
    }
    
    /// <summary>
    /// Execute Delegate 0 Allocated Memory
    /// </summary>
    /// <typeparam name="T0">Argument returned</typeparam>
    /// <typeparam name="T1">Argument returned</typeparam>
    /// <typeparam name="T2">Argument returned</typeparam>
    public class CallbackWrapper<T0, T1, T2>
    {
        private object m_Target;
        private object m_Callback;
        private event Action<CallbackWrapper<T0, T1, T2>, T0, T1, T2> m_CallbackListener;

        public void Add<T>(T target, Action<T, T0, T1, T2> callback) where T : class
        {
            m_Target = target;
            m_Callback = callback;
            m_CallbackListener = null;
            m_CallbackListener += (@this, arg0, arg1, arg2) 
                => (@this.m_Callback as Action<T, T0, T1, T2>)?.Invoke(@this.m_Target as T, arg0, arg1, arg2);
        }

        public void Release()
        {
            m_CallbackListener = null;
            m_Callback = null;
            m_Target = null;
        }

        public CallbackWrapper<T0, T1, T2> Execute(T0 arg0, T1 arg1, T2 arg2)
        {
            m_CallbackListener?.Invoke(this, arg0, arg1, arg2);
            return this;
        }
    }
    
    /// <summary>
    /// Execute Delegate 0 Allocated Memory
    /// </summary>
    /// <typeparam name="T0">Argument returned</typeparam>
    /// <typeparam name="T1">Argument returned</typeparam>
    /// <typeparam name="T2">Argument returned</typeparam>
    /// <typeparam name="T3">Argument returned</typeparam>
    public class CallbackWrapper<T0, T1, T2, T3>
    {
        private object m_Target;
        private object m_Callback;
        private event Action<CallbackWrapper<T0, T1, T2, T3>, T0, T1, T2, T3> m_CallbackListener;

        public void Add<T>(T target, Action<T, T0, T1, T2, T3> callback) where T : class
        {
            m_Target = target;
            m_Callback = callback;
            m_CallbackListener = null;
            m_CallbackListener += (@this, arg0, arg1, arg2, arg3) 
                => (@this.m_Callback as Action<T, T0, T1, T2, T3>)?.Invoke(@this.m_Target as T, arg0, arg1, arg2, arg3);
        }

        public void Release()
        {
            m_CallbackListener = null;
            m_Callback = null;
            m_Target = null;
        }

        public CallbackWrapper<T0, T1, T2, T3> Execute(T0 arg0, T1 arg1, T2 arg2, T3 arg3)
        {
            m_CallbackListener?.Invoke(this, arg0, arg1, arg2, arg3);
            return this;
        }
    }
}